//
//  CarePlanTimelineService.swift
//  
//
//  Created by Augment Agent on 6/21/25.
//

import Foundation
import Vapor
import Fluent

/// Service responsible for creating timeline entries for careplan-related operations
struct CarePlanTimelineService {
    
    // MARK: - Timeline Creation Methods
    
    /// Creates a timeline entry for CarePlan operations
    static func createCarePlanTimeline(
        operation: TimelineOperation,
        carePlan: CarePlan,
        on db: Database,
        memberID: UUID? = nil
    ) async throws {
        let carePlanID = try carePlan.requireID()

        let timelineItem = TimelineItem(
            carepackageID: "careplan-\(carePlanID.uuidString)",
            status: operation.status,
            desc: operation.description(for: "Care Plan", details: "Status: \(carePlan.status)"),
            title: operation.title(for: "Care Plan"),
            memberId: memberID ?? carePlan.$member.id,
            visible: true,
            meta: MetaData(data: [
                "ref_id": carePlanID.uuidString,
                "entity_type": "CarePlan",
                "operation": operation.rawValue
            ])
        )

        timelineItem.$carePlan.id = carePlanID
        try await timelineItem.save(on: db)
    }
    
    /// Creates a timeline entry for Goal operations
    static func createGoalTimeline(
        operation: TimelineOperation,
        goal: Goal,
        on db: Database,
        memberID: UUID? = nil
    ) async throws {
        let goalID = try goal.requireID()
        let carePlanID = goal.$carePlan.id

        let timelineItem = TimelineItem(
            carepackageID: "careplan-\(carePlanID.uuidString)",
            status: operation.status,
            desc: operation.description(for: "Goal", details: goal.description),
            title: operation.title(for: "Goal"),
            memberId: memberID,
            visible: true,
            meta: MetaData(data: [
                "ref_id": goalID.uuidString,
                "entity_type": "Goal",
                "operation": operation.rawValue,
                "care_plan_id": carePlanID.uuidString
            ])
        )

        timelineItem.$carePlan.id = carePlanID
        try await timelineItem.save(on: db)
    }
    
    /// Creates a timeline entry for Intervention operations
    static func createInterventionTimeline(
        operation: TimelineOperation,
        intervention: Intervention,
        on db: Database,
        memberID: UUID? = nil
    ) async throws {
        let timelineItem = TimelineItem()
        timelineItem.$carePlan.id = intervention.$carePlan.id
        timelineItem.carepackageID = "careplan-\(intervention.$carePlan.id.uuidString)"
        timelineItem.status = operation.status
        timelineItem.title = operation.title(for: "Intervention")
        timelineItem.desc = operation.description(for: "Intervention", details: intervention.action)
        timelineItem.visible = true
        timelineItem.memberId = memberID
        timelineItem.meta = MetaData(data: [
            "ref_id": try intervention.requireID().uuidString,
            "entity_type": "Intervention",
            "operation": operation.rawValue,
            "care_plan_id": intervention.$carePlan.id.uuidString
        ])

        try await timelineItem.save(on: db)
    }
    
    /// Creates a timeline entry for Problem operations
    static func createProblemTimeline(
        operation: TimelineOperation,
        problem: Problem,
        on db: Database,
        memberID: UUID? = nil
    ) async throws {
        let problemID = try problem.requireID()
        let carePlanID = problem.$carePlan.id

        let timelineItem = TimelineItem(
            carepackageID: "careplan-\(carePlanID.uuidString)",
            status: operation.status,
            desc: operation.description(for: "Problem", details: problem.description),
            title: operation.title(for: "Problem"),
            memberId: memberID,
            visible: true,
            meta: MetaData(data: [
                "ref_id": problemID.uuidString,
                "entity_type": "Problem",
                "operation": operation.rawValue,
                "care_plan_id": carePlanID.uuidString
            ])
        )

        timelineItem.$carePlan.id = carePlanID
        try await timelineItem.save(on: db)
    }
    
    /// Creates a timeline entry for CareTeamMember operations
    static func createCareTeamMemberTimeline(
        operation: TimelineOperation,
        member: CareTeamMember,
        on db: Database,
        memberID: UUID? = nil
    ) async throws {
        let timelineItem = TimelineItem()
        timelineItem.$carePlan.id = member.$carePlan.id
        timelineItem.carepackageID = "careplan-\(member.$carePlan.id.uuidString)"
        timelineItem.status = operation.status
        timelineItem.title = operation.title(for: "Care Team Member")
        timelineItem.desc = operation.description(for: "Care Team Member", details: "\(member.name) - \(member.role)")
        timelineItem.visible = true
        timelineItem.memberId = memberID
        timelineItem.meta = MetaData(data: [
            "ref_id": try member.requireID().uuidString,
            "entity_type": "CareTeamMember",
            "operation": operation.rawValue,
            "care_plan_id": member.$carePlan.id.uuidString
        ])

        try await timelineItem.save(on: db)
    }
    
    /// Creates a timeline entry for CarePlanReview operations
    static func createCarePlanReviewTimeline(
        operation: TimelineOperation,
        review: CarePlanReview,
        on db: Database,
        memberID: UUID? = nil
    ) async throws {
        let timelineItem = TimelineItem()
        timelineItem.$carePlan.id = review.$carePlan.id
        timelineItem.carepackageID = "careplan-\(review.$carePlan.id.uuidString)"
        timelineItem.status = operation.status
        timelineItem.title = operation.title(for: "Care Plan Review")
        timelineItem.desc = operation.description(for: "Care Plan Review", details: "Reviewed by \(review.reviewerName)")
        timelineItem.visible = true
        timelineItem.memberId = memberID
        timelineItem.meta = MetaData(data: [
            "ref_id": try review.requireID().uuidString,
            "entity_type": "CarePlanReview",
            "operation": operation.rawValue,
            "care_plan_id": review.$carePlan.id.uuidString
        ])
        
        try await timelineItem.save(on: db)
    }
    
    /// Creates a timeline entry for CarePlanService operations
    static func createCarePlanServiceTimeline(
        operation: TimelineOperation,
        service: CarePlanService,
        on db: Database,
        memberID: UUID? = nil
    ) async throws {
        let timelineItem = TimelineItem()
        timelineItem.$carePlan.id = service.$carePlan.id
        timelineItem.carepackageID = "careplan-\(service.$carePlan.id.uuidString)"
        timelineItem.status = operation.status
        timelineItem.title = operation.title(for: "Care Plan Service")
        timelineItem.desc = operation.description(for: "Care Plan Service", details: "\(service.cboName) - \(service.staffName)")
        timelineItem.visible = true
        timelineItem.memberId = memberID
        timelineItem.meta = MetaData(data: [
            "ref_id": try service.requireID().uuidString,
            "entity_type": "CarePlanService",
            "operation": operation.rawValue,
            "care_plan_id": service.$carePlan.id.uuidString
        ])
        
        try await timelineItem.save(on: db)
    }
    
    /// Creates a timeline entry for CarePlanFollowUp operations
    static func createCarePlanFollowUpTimeline(
        operation: TimelineOperation,
        followUp: CarePlanFollowUp,
        on db: Database,
        memberID: UUID? = nil
    ) async throws {
        let timelineItem = TimelineItem()
        timelineItem.$carePlan.id = followUp.$carePlan.id
        timelineItem.carepackageID = "careplan-\(followUp.$carePlan.id.uuidString)"
        timelineItem.status = operation.status
        timelineItem.title = operation.title(for: "Follow-up")
        timelineItem.desc = operation.description(for: "Follow-up", details: "\(followUp.type) by \(followUp.staffName)")
        timelineItem.visible = true
        timelineItem.memberId = memberID
        timelineItem.meta = MetaData(data: [
            "ref_id": try followUp.requireID().uuidString,
            "entity_type": "CarePlanFollowUp",
            "operation": operation.rawValue,
            "care_plan_id": followUp.$carePlan.id.uuidString
        ])
        
        try await timelineItem.save(on: db)
    }
}

// MARK: - Timeline Operation Enum

enum TimelineOperation: String, CaseIterable {
    case created = "created"
    case updated = "updated"
    case deleted = "deleted"
    
    var status: String {
        switch self {
        case .created:
            return "created"
        case .updated:
            return "updated"
        case .deleted:
            return "deleted"
        }
    }
    
    func title(for entityType: String) -> String {
        switch self {
        case .created:
            return "\(entityType) Created"
        case .updated:
            return "\(entityType) Updated"
        case .deleted:
            return "\(entityType) Deleted"
        }
    }
    
    func description(for entityType: String, details: String) -> String {
        switch self {
        case .created:
            return "\(entityType) was created: \(details)"
        case .updated:
            return "\(entityType) was updated: \(details)"
        case .deleted:
            return "\(entityType) was deleted: \(details)"
        }
    }
}
