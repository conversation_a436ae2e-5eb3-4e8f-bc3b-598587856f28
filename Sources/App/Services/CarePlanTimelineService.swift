//
//  CarePlanTimelineService.swift
//  
//
//  Created by Augment Agent on 6/21/25.
//

import Foundation
import Vapor
import Fluent

/// Service responsible for creating timeline entries for careplan-related operations
struct CarePlanTimelineService {
    
    // MARK: - Timeline Creation Methods
    
    /// Creates a timeline entry for CarePlan operations
    static func createCarePlanTimeline(
        operation: TimelineOperation,
        carePlan: CarePlan,
        on db: Database,
        creatorID: UUID,
        memberID: UUID? = nil
    ) async throws {
        let carePlanID = try carePlan.requireID()

        let timelineItem = TimelineItem(
            carepackageID: "careplan-\(carePlanID.uuidString)",
            status: operation.status,
            desc: operation.description(for: "Care Plan", details: "Status: \(carePlan.status)"),
            title: operation.title(for: "Care Plan"),
            memberId: memberID ?? carePlan.$member.id,
            visible: true,
            meta: MetaData(data: [
                "ref_id": carePlanID.uuidString,
                "entity_type": "CarePlan",
                "operation": operation.rawValue
            ])
        )

        timelineItem.$carePlan.id = carePlanID
        try await timelineItem.create(on: db)

        // Set creator relationship after timeline item is created
        timelineItem.$creator.id = creatorID
        try await timelineItem.update(on: db)
    }
    
    /// Creates a timeline entry for Goal operations
    static func createGoalTimeline(
        operation: TimelineOperation,
        goal: Goal,
        on db: Database,
        creatorID: UUID,
        memberID: UUID? = nil
    ) async throws {
        let goalID = try goal.requireID()
        let carePlanID = goal.$carePlan.id

        let timelineItem = TimelineItem(
            carepackageID: "careplan-\(carePlanID.uuidString)",
            status: operation.status,
            desc: operation.description(for: "Goal", details: goal.description),
            title: operation.title(for: "Goal"),
            memberId: memberID,
            visible: true,
            meta: MetaData(data: [
                "ref_id": goalID.uuidString,
                "entity_type": "Goal",
                "operation": operation.rawValue,
                "care_plan_id": carePlanID.uuidString
            ])
        )

        timelineItem.$carePlan.id = carePlanID
        try await timelineItem.save(on: db)

        // Set creator relationship after timeline item is created
        timelineItem.$creator.id = creatorID
        try await timelineItem.update(on: db)
    }
    
    /// Creates a timeline entry for Intervention operations
    static func createInterventionTimeline(
        operation: TimelineOperation,
        intervention: Intervention,
        on db: Database,
        creatorID: UUID,
        memberID: UUID? = nil
    ) async throws {
        let interventionID = try intervention.requireID()
        let carePlanID = intervention.$carePlan.id

        let timelineItem = TimelineItem(
            carepackageID: "careplan-\(carePlanID.uuidString)",
            status: operation.status,
            desc: operation.description(for: "Intervention", details: intervention.action),
            title: operation.title(for: "Intervention"),
            memberId: memberID,
            visible: true,
            meta: MetaData(data: [
                "ref_id": interventionID.uuidString,
                "entity_type": "Intervention",
                "operation": operation.rawValue,
                "care_plan_id": carePlanID.uuidString
            ])
        )

        timelineItem.$carePlan.id = carePlanID
        try await timelineItem.save(on: db)

        // Set creator relationship after timeline item is created
        timelineItem.$creator.id = creatorID
        try await timelineItem.update(on: db)
    }
    
    /// Creates a timeline entry for Problem operations
    static func createProblemTimeline(
        operation: TimelineOperation,
        problem: Problem,
        on db: Database,
        creatorID: UUID,
        memberID: UUID? = nil
    ) async throws {
        let problemID = try problem.requireID()
        let carePlanID = problem.$carePlan.id

        let timelineItem = TimelineItem(
            carepackageID: "careplan-\(carePlanID.uuidString)",
            status: operation.status,
            desc: operation.description(for: "Problem", details: problem.description),
            title: operation.title(for: "Problem"),
            memberId: memberID,
            visible: true,
            meta: MetaData(data: [
                "ref_id": problemID.uuidString,
                "entity_type": "Problem",
                "operation": operation.rawValue,
                "care_plan_id": carePlanID.uuidString
            ])
        )

        timelineItem.$carePlan.id = carePlanID
        try await timelineItem.save(on: db)

        // Set creator relationship after timeline item is created
        timelineItem.$creator.id = creatorID
        try await timelineItem.update(on: db)
    }
    
    /// Creates a timeline entry for CareTeamMember operations
    static func createCareTeamMemberTimeline(
        operation: TimelineOperation,
        member: CareTeamMember,
        on db: Database,
        creatorID: UUID,
        memberID: UUID? = nil
    ) async throws {
        let memberID_entity = try member.requireID()
        let carePlanID = member.$carePlan.id

        let timelineItem = TimelineItem(
            carepackageID: "careplan-\(carePlanID.uuidString)",
            status: operation.status,
            desc: operation.description(for: "Care Team Member", details: "\(member.name) - \(member.role)"),
            title: operation.title(for: "Care Team Member"),
            memberId: memberID,
            visible: true,
            meta: MetaData(data: [
                "ref_id": memberID_entity.uuidString,
                "entity_type": "CareTeamMember",
                "operation": operation.rawValue,
                "care_plan_id": carePlanID.uuidString
            ])
        )

        timelineItem.$carePlan.id = carePlanID
        try await timelineItem.save(on: db)

        // Set creator relationship after timeline item is created
        timelineItem.$creator.id = creatorID
        try await timelineItem.update(on: db)
    }
    
    /// Creates a timeline entry for CarePlanReview operations
    static func createCarePlanReviewTimeline(
        operation: TimelineOperation,
        review: CarePlanReview,
        on db: Database,
        creatorID: UUID,
        memberID: UUID? = nil
    ) async throws {
        let reviewID = try review.requireID()
        let carePlanID = review.$carePlan.id

        let timelineItem = TimelineItem(
            carepackageID: "careplan-\(carePlanID.uuidString)",
            status: operation.status,
            desc: operation.description(for: "Care Plan Review", details: "Reviewed by \(review.reviewerName)"),
            title: operation.title(for: "Care Plan Review"),
            memberId: memberID,
            visible: true,
            meta: MetaData(data: [
                "ref_id": reviewID.uuidString,
                "entity_type": "CarePlanReview",
                "operation": operation.rawValue,
                "care_plan_id": carePlanID.uuidString
            ])
        )

        timelineItem.$carePlan.id = carePlanID
        try await timelineItem.save(on: db)

        // Set creator relationship after timeline item is created
        timelineItem.$creator.id = creatorID
        try await timelineItem.update(on: db)
    }
    
    /// Creates a timeline entry for CarePlanService operations
    static func createCarePlanServiceTimeline(
        operation: TimelineOperation,
        service: CarePlanService,
        on db: Database,
        creatorID: UUID,
        memberID: UUID? = nil
    ) async throws {
        let serviceID = try service.requireID()
        let carePlanID = service.$carePlan.id

        let timelineItem = TimelineItem(
            carepackageID: "careplan-\(carePlanID.uuidString)",
            status: operation.status,
            desc: operation.description(for: "Care Plan Service", details: "\(service.cboName) - \(service.staffName)"),
            title: operation.title(for: "Care Plan Service"),
            memberId: memberID,
            visible: true,
            meta: MetaData(data: [
                "ref_id": serviceID.uuidString,
                "entity_type": "CarePlanService",
                "operation": operation.rawValue,
                "care_plan_id": carePlanID.uuidString
            ])
        )

        timelineItem.$carePlan.id = carePlanID
        try await timelineItem.save(on: db)

        // Set creator relationship after timeline item is created
        timelineItem.$creator.id = creatorID
        try await timelineItem.update(on: db)
    }
    
    /// Creates a timeline entry for CarePlanFollowUp operations
    static func createCarePlanFollowUpTimeline(
        operation: TimelineOperation,
        followUp: CarePlanFollowUp,
        on db: Database,
        creatorID: UUID,
        memberID: UUID? = nil
    ) async throws {
        let followUpID = try followUp.requireID()
        let carePlanID = followUp.$carePlan.id

        let timelineItem = TimelineItem(
            carepackageID: "careplan-\(carePlanID.uuidString)",
            status: operation.status,
            desc: operation.description(for: "Follow-up", details: "\(followUp.type) by \(followUp.staffName)"),
            title: operation.title(for: "Follow-up"),
            memberId: memberID,
            visible: true,
            meta: MetaData(data: [
                "ref_id": followUpID.uuidString,
                "entity_type": "CarePlanFollowUp",
                "operation": operation.rawValue,
                "care_plan_id": carePlanID.uuidString
            ])
        )

        timelineItem.$carePlan.id = carePlanID
        try await timelineItem.save(on: db)

        // Set creator relationship after timeline item is created
        timelineItem.$creator.id = creatorID
        try await timelineItem.update(on: db)
    }
}

// MARK: - Timeline Operation Enum

enum TimelineOperation: String, CaseIterable {
    case created = "created"
    case updated = "updated"
    case deleted = "deleted"
    
    var status: String {
        switch self {
        case .created:
            return "created"
        case .updated:
            return "updated"
        case .deleted:
            return "deleted"
        }
    }
    
    func title(for entityType: String) -> String {
        switch self {
        case .created:
            return "\(entityType) Created"
        case .updated:
            return "\(entityType) Updated"
        case .deleted:
            return "\(entityType) Deleted"
        }
    }
    
    func description(for entityType: String, details: String) -> String {
        switch self {
        case .created:
            return "\(entityType) was created: \(details)"
        case .updated:
            return "\(entityType) was updated: \(details)"
        case .deleted:
            return "\(entityType) was deleted: \(details)"
        }
    }
}
